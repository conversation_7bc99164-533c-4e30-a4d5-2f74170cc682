<template>
  <div class="mod-dashboard">
    <!-- Modern Header with Search and Filters -->
    <header class="dashboard-header">
      <div class="header-content">
        <!-- Search Bar -->
        <div class="search-section">
          <div class="search-input-wrapper">
            <MagnifyingGlassIcon class="search-icon" />
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search your mod collection..."
              class="search-input"
              aria-label="Search mods"
            />
            <button
              v-if="searchQuery"
              @click="clearSearch"
              class="search-clear"
              aria-label="Clear search"
            >
              <XMarkIcon />
            </button>
          </div>
        </div>

        <!-- Filters and Controls -->
        <div class="header-controls">
          <div class="filter-section">
            <select v-model="selectedFileTypeFilter" class="filter-select" aria-label="Filter by file type">
              <option value="">All Types</option>
              <option value=".package">Package Files</option>
              <option value=".ts4script">Script Files</option>
            </select>

            <select v-model="selectedQualityFilter" class="filter-select" aria-label="Filter by quality">
              <option value="">All Quality</option>
              <option value="excellent">Excellent (90-100)</option>
              <option value="good">Good (70-89)</option>
              <option value="fair">Fair (50-69)</option>
              <option value="poor">Poor (0-49)</option>
            </select>

            <select v-model="selectedSortOption" class="filter-select" aria-label="Sort options">
              <option value="name">Sort by Name</option>
              <option value="quality">Sort by Quality</option>
              <option value="size">Sort by Size</option>
              <option value="author">Sort by Author</option>
            </select>
          </div>

          <!-- Results Info and Thumbnail Size Controls -->
          <div class="header-info">
            <div class="results-info">
              <span class="results-count">{{ filteredMods?.length || 0 }}</span>
              <span class="results-text">mods</span>
              <span v-if="hasActiveFilters" class="results-filtered">(filtered)</span>
            </div>

            <div class="thumbnail-size-controls">
              <button
                @click="thumbnailSize = 'small'"
                :class="{ active: thumbnailSize === 'small' }"
                class="size-btn"
                title="Small thumbnails"
                aria-label="Small thumbnail size"
              >
                <Squares2X2Icon />
              </button>
              <button
                @click="thumbnailSize = 'medium'"
                :class="{ active: thumbnailSize === 'medium' }"
                class="size-btn"
                title="Medium thumbnails"
                aria-label="Medium thumbnail size"
              >
                <Square3Stack3DIcon />
              </button>
              <button
                @click="thumbnailSize = 'large'"
                :class="{ active: thumbnailSize === 'large' }"
                class="size-btn"
                title="Large thumbnails"
                aria-label="Large thumbnail size"
              >
                <RectangleStackIcon />
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>
    






    <!-- Main Content Area -->
    <main class="dashboard-main">
      <!-- Loading State -->
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <p class="loading-text">Analyzing mods with advanced intelligence...</p>
      </div>

      <!-- Thumbnail Grid (Primary View) -->
      <div v-else-if="filteredMods.length > 0" class="thumbnail-gallery">
        <div
          class="thumbnail-grid"
          :class="`thumbnail-grid--${thumbnailSize}`"
          role="grid"
          aria-label="Mod collection"
        >
          <article
            v-for="(mod, index) in paginatedMods"
            :key="mod.fileName || index"
            class="thumbnail-item"
            role="gridcell"
            :tabindex="0"
            @click="openModDetails(mod)"
            @keydown.enter="openModDetails(mod)"
            @keydown.space.prevent="openModDetails(mod)"
            :aria-label="`${getModDisplayName(mod)} by ${mod?.author || 'Unknown'}`"
          >
            <div class="thumbnail-container">
              <!-- Thumbnail Image -->
              <div class="thumbnail-image">
                <img
                  v-if="mod?.thumbnailUrl"
                  :src="mod.thumbnailUrl"
                  :alt="`${getModDisplayName(mod)} thumbnail`"
                  class="thumbnail-img"
                  loading="lazy"
                  @error="handleThumbnailError(mod)"
                />
                <div v-else class="thumbnail-fallback">
                  <component :is="getCategoryIcon(mod)" class="fallback-icon" />
                </div>
              </div>

              <!-- Thumbnail Overlay -->
              <div class="thumbnail-overlay">
                <h3 class="thumbnail-title">{{ getModDisplayName(mod) }}</h3>
                <p v-if="mod?.author" class="thumbnail-author">by {{ mod.author }}</p>
              </div>

              <!-- Quality Badge -->
              <div v-if="getQualityScore(mod)" class="quality-badge" :class="getQualityClass(mod)">
                {{ getQualityScore(mod) }}
              </div>

              <!-- File Type Badge -->
              <div class="file-type-badge" :class="getFileTypeClass(mod)">
                {{ getFileTypeLabel(mod) }}
              </div>
            </div>
          </article>
        </div>

        <!-- Pagination -->
        <nav v-if="totalPages > 1" class="pagination" aria-label="Pagination navigation">
          <button
            @click="currentPage = Math.max(1, currentPage - 1)"
            :disabled="currentPage === 1"
            class="pagination-btn"
            aria-label="Previous page"
          >
            Previous
          </button>
          <span class="pagination-info">
            Page {{ currentPage }} of {{ totalPages }}
          </span>
          <button
            @click="currentPage = Math.min(totalPages, currentPage + 1)"
            :disabled="currentPage === totalPages"
            class="pagination-btn"
            aria-label="Next page"
          >
            Next
          </button>
        </nav>
      </div>

      <!-- Empty State -->
      <div v-else class="empty-state">
        <div class="empty-state__icon">
          <FolderOpenIcon />
        </div>
        <h2 class="empty-state__title">No mods found</h2>
        <p class="empty-state__description">
          {{ hasActiveFilters ?
            'Try adjusting your filters to see more results.' :
            'Start by analyzing your Sims 4 mods folder to explore your collection.' }}
        </p>
        <button v-if="hasActiveFilters" @click="clearAllFilters" class="empty-state__action">
          Clear All Filters
        </button>
      </div>
    </main>

    <!-- Mod Details Modal -->
    <div v-if="selectedMod" class="mod-details-modal" @click.self="closeModDetails">
      <div class="mod-details-content" role="dialog" aria-modal="true" :aria-label="`Details for ${getModDisplayName(selectedMod)}`">
        <header class="mod-details-header">
          <div class="mod-details-title-section">
            <h2 class="mod-details-title">{{ getModDisplayName(selectedMod) }}</h2>
            <p v-if="selectedMod?.author" class="mod-details-author">by {{ selectedMod.author }}</p>
          </div>
          <button @click="closeModDetails" class="mod-details-close" aria-label="Close details">
            <XMarkIcon />
          </button>
        </header>

        <div class="mod-details-body">
          <!-- Mod Image/Thumbnail -->
          <div class="mod-details-image">
            <img
              v-if="selectedMod?.thumbnailUrl"
              :src="selectedMod.thumbnailUrl"
              :alt="`${getModDisplayName(selectedMod)} preview`"
              class="mod-preview-img"
            />
            <div v-else class="mod-preview-fallback">
              <component :is="getCategoryIcon(selectedMod)" class="preview-fallback-icon" />
            </div>
          </div>

          <!-- Mod Information -->
          <div class="mod-details-info">
            <div class="mod-info-grid">
              <div class="mod-info-item">
                <span class="mod-info-label">File Name</span>
                <span class="mod-info-value">{{ selectedMod?.fileName || 'Unknown' }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Size</span>
                <span class="mod-info-value">{{ formatFileSize(selectedMod?.fileSize || 0) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">File Type</span>
                <span class="mod-info-value">{{ getFileTypeLabel(selectedMod) }}</span>
              </div>
              <div v-if="selectedMod?.version" class="mod-info-item">
                <span class="mod-info-label">Version</span>
                <span class="mod-info-value">{{ selectedMod?.version || 'Unknown' }}</span>
              </div>
              <div v-if="getQualityScore(selectedMod)" class="mod-info-item">
                <span class="mod-info-label">Quality Score</span>
                <span class="mod-info-value">{{ getQualityScore(selectedMod) }}</span>
              </div>
              <div class="mod-info-item">
                <span class="mod-info-label">Category</span>
                <span class="mod-info-value">{{ getModCategory(selectedMod) }}</span>
              </div>
            </div>

            <!-- Additional Details -->
            <div v-if="selectedMod?.resourceCount" class="mod-additional-info">
              <h3>Technical Details</h3>
              <p>Resources: {{ selectedMod?.resourceCount || 0 }}</p>
              <p v-if="selectedMod?.processingTime">Processing Time: {{ selectedMod.processingTime }}ms</p>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, shallowRef, shallowReactive } from 'vue';
import {
  MagnifyingGlassIcon,
  XMarkIcon,
  Squares2X2Icon,
  Square3Stack3DIcon,
  RectangleStackIcon,
  FolderOpenIcon,
  // Category icons
  UserIcon,
  HomeIcon,
  CogIcon,
  CommandLineIcon,
  CubeIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline';

// Props
const props = defineProps<{
  mods?: any[];
  isLoading?: boolean;
}>();

// Use computed for reactive props - ensure reactivity
const mods = computed(() => {
  const modsData = props.mods || [];
  return modsData;
});
const isLoading = computed(() => props.isLoading || false);

// Watch for loading state changes
watch(() => props.isLoading, (newLoading, oldLoading) => {
  // Loading state changed - trigger reactivity
}, { immediate: true });

// Reactive state - using shallow reactivity for performance
const searchQuery = ref('');
const selectedFileTypeFilter = ref('');
const selectedQualityFilter = ref('');
const selectedSortOption = ref('name');
const currentPage = ref(1);
const itemsPerPage = ref(24); // Better for grid layouts

// Thumbnail-first specific state
const thumbnailSize = ref<'small' | 'medium' | 'large'>('medium');
const selectedMod = ref<any>(null);

// Use shallowReactive for collections that change frequently
const modThumbnails = shallowRef<any[]>([]);
const selectedMods = shallowReactive(new Set<string>());

// Computed properties
const totalMods = computed(() => {
  return mods.value?.length || 0;
});



// Use shallowRef for filtered results to optimize performance
const filteredMods = shallowRef<any[]>([]);

// Watch for changes and update filtered mods
watch(
  [mods, searchQuery, selectedFileTypeFilter, selectedQualityFilter, selectedSortOption],
  () => {
    if (!mods.value || !Array.isArray(mods.value)) {
      filteredMods.value = [];
      return;
    }

    let filtered = [...mods.value];

    // Search filter
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(mod =>
        (mod.fileName && mod.fileName.toLowerCase().includes(query)) ||
        (mod.author && mod.author.toLowerCase().includes(query)) ||
        (mod.modName && mod.modName.toLowerCase().includes(query))
      );
    }

    // File type filter
    if (selectedFileTypeFilter.value) {
      filtered = filtered.filter(mod =>
        mod && mod.fileExtension === selectedFileTypeFilter.value
      );
    }

  // Quality filter
  if (selectedQualityFilter.value) {
    filtered = filtered.filter(mod => {
      if (!mod || typeof mod.qualityScore !== 'number') return false;
      const score = mod.qualityScore;
      switch (selectedQualityFilter.value) {
        case 'excellent': return score >= 90;
        case 'good': return score >= 70 && score < 90;
        case 'fair': return score >= 50 && score < 70;
        case 'poor': return score < 50;
        case 'high': return score >= 80;
        case 'medium': return score >= 60 && score < 80;
        case 'low': return score < 60;
        default: return true;
      }
    });
  }

    // Sort
    filtered.sort((a, b) => {
      switch (selectedSortOption.value) {
        case 'name':
          return (a.fileName || '').localeCompare(b.fileName || '');
        case 'quality':
          return (b.qualityScore || 0) - (a.qualityScore || 0);
        case 'size':
          return (b.fileSize || 0) - (a.fileSize || 0);
        case 'author':
          return (a.author || '').localeCompare(b.author || '');
        case 'intelligence':
          return (a.intelligenceType || '').localeCompare(b.intelligenceType || '');
        default:
          return 0;
      }
    });

    filteredMods.value = filtered;
  },
  { immediate: true }
);

const totalPages = computed(() =>
  Math.ceil((filteredMods.value?.length || 0) / itemsPerPage.value)
);

// Use shallowRef for paginated results
const paginatedMods = shallowRef<any[]>([]);

// Watch for pagination changes
watch(
  [filteredMods, currentPage, itemsPerPage],
  () => {
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    paginatedMods.value = filteredMods.value?.slice(start, end) || [];
  },
  { immediate: true }
);

const hasActiveFilters = computed(() =>
  searchQuery.value ||
  selectedFileTypeFilter.value ||
  selectedQualityFilter.value
);



// Watch for filtered mods changes (moved here after computed properties are defined)
watch(filteredMods, (newFiltered, oldFiltered) => {
  // Filtered mods changed - trigger reactivity
}, { immediate: true });

// Methods
const getModDisplayName = (mod: any): string => {
  // Priority 1: Use actual mod name from StringTable analysis if available
  if (mod.actualModName && mod.actualModName !== 'Unknown Mod') {
    return mod.actualModName;
  }

  // Priority 2: Use metadata mod name if available
  if (mod.modName && mod.modName !== 'Unknown Mod') {
    return mod.modName;
  }

  // Priority 3: Clean up the filename
  if (mod.fileName) {
    let name = mod.fileName
      .replace(/\.(package|ts4script)$/i, '') // Remove file extension
      .replace(/\[.*?\]/g, '') // Remove brackets and content (like [Author])
      .replace(/\{.*?\}/g, '') // Remove curly braces and content
      .replace(/\(.*?\)/g, '') // Remove parentheses and content
      .replace(/_+/g, ' ') // Replace underscores with spaces
      .replace(/-+/g, ' ') // Replace dashes with spaces
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    // Clean up common prefixes/suffixes
    name = name
      .replace(/^(mod|package|script|cc|custom|content)\s+/i, '') // Remove common prefixes
      .replace(/\s+(mod|package|script|cc|custom|content)$/i, '') // Remove common suffixes
      .replace(/^(the|a|an)\s+/i, '') // Remove articles at start
      .trim();

    // If we still have a meaningful name, format it nicely
    if (name && name.length > 0 && name !== 'Unknown') {
      // Handle special cases like "newemotionaltraits - arroganttrait"
      if (name.includes(' - ')) {
        return name.split(' - ')
          .map(part => part.split(' ')
            .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
            .filter(word => word.length > 0)
            .join(' '))
          .filter(part => part.length > 0)
          .join(' - ');
      } else {
        // Capitalize first letter of each word
        return name.split(' ')
          .map(word => word && word.length > 0 ? word.charAt(0).toUpperCase() + word.slice(1).toLowerCase() : '')
          .filter(word => word.length > 0)
          .join(' ');
      }
    }
  }

  return 'Unknown Mod';
};

const getQualityScore = (mod: any): string => {
  if (!mod) return '';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore;
  if (typeof score === 'number') {
    // Quality scores are already in 0-100 range from QualityAssessmentAnalyzer
    const percentage = Math.round(Math.max(0, Math.min(100, score)));
    return `${percentage}%`;
  }
  return '';
};

const getQualityClass = (mod: any): string => {
  if (!mod) return 'quality-poor';
  const score = mod.qualityAssessmentData?.overallScore || mod.qualityScore || 0;
  if (score >= 0.8) return 'quality-excellent';
  if (score >= 0.6) return 'quality-good';
  if (score >= 0.4) return 'quality-fair';
  return 'quality-poor';
};

const getCategoryIcon = (mod: any) => {
  // Return appropriate icon component based on mod category
  // For now, return a default icon - you can expand this based on categories
  return 'CubeIcon'; // Default icon
};

const getFileTypeClass = (mod: any): string => {
  if (!mod) return 'file-type-package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'file-type-script' : 'file-type-package';
};

const getFileTypeLabel = (mod: any): string => {
  if (!mod) return 'Package';
  const extension = mod.fileExtension || '.package';
  return extension === '.ts4script' ? 'Script' : 'Package';
};

const getModCategory = (mod: any): string => {
  if (!mod) return 'Unknown';
  // Try to get category from various sources
  if (mod.universalClassification?.category) return mod.universalClassification.category;
  if (mod.objectClassification?.category) return mod.objectClassification.category;
  if (mod.category) return mod.category;
  return 'Unknown';
};

const clearSearch = () => {
  searchQuery.value = '';
};

const clearAllFilters = () => {
  searchQuery.value = '';
  selectedFileTypeFilter.value = '';
  selectedQualityFilter.value = '';
  currentPage.value = 1;
};

const handleSort = (field: string) => {
  selectedSortOption.value = field;
  currentPage.value = 1;
};

const formatBytes = (bytes: number | null | undefined): string => {
  if (!bytes || bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
};

const formatFileSize = (bytes: number | null | undefined): string => {
  return formatBytes(bytes);
};

// Modal and interaction methods
const openModDetails = (mod: any) => {
  if (mod) {
    selectedMod.value = mod;
  }
};

const closeModDetails = () => {
  selectedMod.value = null;
};

const handleThumbnailError = (mod: any) => {
  // Could set a fallback thumbnail URL here
};

// Watch for filter changes to reset pagination
const resetPagination = () => {
  currentPage.value = 1;
};

// Thumbnail methods
const openImagePreview = (thumbnail: any) => {
  const index = modThumbnails.value.findIndex(t => t.id === thumbnail.id);
  if (index !== -1) {
    previewThumbnailIndex.value = index;
    showImagePreview.value = true;
  }
};

const closeImagePreview = () => {
  showImagePreview.value = false;
};



const showModDetails = (thumbnail: any) => {
  if (!thumbnail || !thumbnail.modFileName) return;

  // Find the corresponding mod and show its details
  const mod = mods.value?.find(m => m && m.fileName === thumbnail.modFileName);
  if (mod) {
    // Could emit an event or show a modal with mod details
  }
};

const onThumbnailLoad = (thumbnail: any) => {
  // Handle successful thumbnail load
};

const onThumbnailError = (thumbnail: any) => {
  // Handle thumbnail load error
};

const extractThumbnails = async () => {
  if (!mods.value || mods.value.length === 0) return;

  isExtractingThumbnails.value = true;
  thumbnailProgress.value = 0;
  modThumbnails.value = [];

  try {
    // Import the thumbnail extraction service
    const { ThumbnailExtractionService } = await import('../../services/visual/ThumbnailExtractionService');

    const totalMods = mods.value.length;
    let processedMods = 0;

    for (const mod of mods.value) {
      try {
        // For now, create mock thumbnails since we don't have actual file buffers
        const mockThumbnail = {
          id: `thumb_${mod.fileName || 'unknown'}_${Date.now()}`,
          modFileName: mod.fileName || 'unknown',
          resourceType: 'mock',
          resourceKey: 'mock_key',
          imageData: createMockThumbnailData(mod),
          format: 'svg' as const,
          width: 200,
          height: 200,
          category: mod.category || 'unknown',
          subcategory: mod.subcategory || null,
          confidence: 0.8,
          extractionMethod: 'fallback_icon' as const,
          fileSize: mod.fileSize || 0,
          isHighQuality: false,
          isFallback: true
        };

        modThumbnails.value.push(mockThumbnail);

      } catch (error) {
        // Failed to extract thumbnail - continue with next mod
      }

      processedMods++;
      thumbnailProgress.value = (processedMods / totalMods) * 100;
    }

  } catch (error) {
    // Error extracting thumbnails - continue without thumbnails
  } finally {
    isExtractingThumbnails.value = false;
  }
};

const createMockThumbnailData = (mod: any): string => {
  const category = mod.category || 'unknown';
  const icons: Record<string, string> = {
    'cas': '👕',
    'objects': '🪑',
    'script': '⚙️',
    'tuning': '🔧',
    'unknown': '📦'
  };

  const icon = icons[category] || icons.unknown;
  const color = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7'][Math.floor(Math.random() * 5)];

  const svg = `<svg width="200" height="200" xmlns="http://www.w3.org/2000/svg">
    <rect width="100%" height="100%" fill="${color}" rx="8"/>
    <text x="50%" y="40%" text-anchor="middle" font-size="48">${icon}</text>
    <text x="50%" y="70%" text-anchor="middle" dy=".3em" fill="white" font-family="Arial" font-size="12">
      ${category.toUpperCase()}
    </text>
  </svg>`;

  return `data:image/svg+xml;base64,${btoa(svg)}`;
};

// Note: Thumbnail extraction is handled automatically when mods are loaded

// Lifecycle
onMounted(() => {
  // Any initialization logic
});
</script>

<style scoped>
/* ===== SIMS 4 COLOR THEMING - BRIGHT & VIBRANT PALETTE ===== */
:root {
  /* Sims 4 Primary Colors - Vibrant and cheerful */
  --plumbob-green: #00D4AA;
  --plumbob-green-light: #26E0B5;
  --plumbob-green-dark: #00B894;
  --plumbob-green-bg: rgba(0, 212, 170, 0.12);

  /* Sims 4 Accent Colors - More vibrant */
  --sims-blue: #0984E3;
  --sims-blue-light: #74B9FF;
  --sims-blue-dark: #0652DD;
  --sims-purple: #A29BFE;
  --sims-pink: #FD79A8;
  --sims-orange: #FDCB6E;
  --sims-yellow: #F1C40F;

  /* Bright, Warm Backgrounds - More inviting */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F8F9FA;
  --bg-tertiary: #E9ECEF;
  --bg-elevated: #FFFFFF;
  --bg-accent: linear-gradient(135deg, #00D4AA 0%, #74B9FF 100%);

  /* Text Colors - Warmer and more inviting */
  --text-primary: #2D3748;
  --text-secondary: #4A5568;
  --text-tertiary: #718096;
  --text-accent: var(--plumbob-green);
  --text-inverse: #FFFFFF;

  /* Border Colors - Softer and more vibrant */
  --border-light: #E9ECEF;
  --border-medium: #DEE2E6;
  --border-strong: #ADB5BD;
  --border-accent: rgba(0, 212, 170, 0.3);

  /* Spacing and Typography */
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;

  /* Apple-Inspired Typography Scale */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 30px;
  --text-4xl: 36px;

  /* Font Weights - Apple System */
  --font-light: 300;
  --font-regular: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Line Heights - Optimized for readability */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Letter Spacing - Apple-inspired */
  --tracking-tighter: -0.05em;
  --tracking-tight: -0.025em;
  --tracking-normal: 0em;
  --tracking-wide: 0.025em;
  --tracking-wider: 0.05em;

  /* Font Families - Apple System Stack */
  --font-family-system: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --font-family-display: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;

  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;
  --radius-full: 9999px;

  --duration-150: 150ms;
  --duration-200: 200ms;
  --ease-out: cubic-bezier(0, 0, 0.2, 1);

  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-vibrant: 0 4px 15px rgba(0, 212, 170, 0.15);
  --shadow-hover: 0 8px 25px rgba(0, 212, 170, 0.2);

  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* Typography Enhancements */
  --text-rendering: optimizeLegibility;
  --font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  --font-variant-ligatures: common-ligatures;
  --font-variant-numeric: tabular-nums;
}

.mod-dashboard {
  min-height: 100vh;
  background:
    linear-gradient(135deg, rgba(0, 212, 170, 0.03) 0%, rgba(116, 185, 255, 0.03) 100%),
    var(--bg-secondary);
  font-family: var(--font-family-system);
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  color: var(--text-primary);
  letter-spacing: var(--tracking-normal);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: var(--text-rendering);
  font-feature-settings: var(--font-feature-settings);
  font-variant-ligatures: var(--font-variant-ligatures);
  font-variant-numeric: var(--font-variant-numeric);
}

/* Main Dashboard Content */



/* Controls */
.dashboard-controls {
  max-width: 1280px;
  margin: 0 auto;
  padding: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.search-section {
  display: flex;
  justify-content: center;
}

.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 600px;
}

.search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  color: var(--text-tertiary);
}

.search-input {
  width: 100%;
  height: 56px;
  padding: 0 var(--space-12) 0 var(--space-12);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-full);
  background: var(--bg-elevated);
  font-family: var(--font-family-system);
  font-size: var(--text-lg);
  font-weight: var(--font-regular);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  transition: all var(--duration-200) var(--ease-out);
  box-shadow: var(--shadow-sm);
}

.search-input::placeholder {
  color: var(--text-tertiary);
  font-weight: var(--font-regular);
  letter-spacing: var(--tracking-normal);
}

.search-input:focus {
  outline: none;
  border-color: var(--plumbob-green);
  box-shadow:
    0 0 0 4px var(--plumbob-green-bg),
    0 4px 6px -1px rgba(0, 212, 170, 0.15),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    var(--shadow-vibrant);
  background: var(--bg-primary);
}

.search-clear {
  position: absolute;
  right: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-clear:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.filter-section {
  display: flex;
  gap: var(--space-4);
  align-items: end;
  flex-wrap: wrap;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-label {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-normal);
}

.filter-select {
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-regular);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  min-width: 150px;
}

.filter-clear-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-medium);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-secondary);
  font-size: var(--text-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.filter-clear-btn:hover {
  background: var(--bg-secondary);
  border-color: var(--border-strong);
}

/* Results */
.results-summary {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-4) var(--space-6);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.results-summary__info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--text-sm);
}

.results-count {
  font-family: var(--font-family-mono);
  font-weight: var(--font-bold);
  color: var(--text-primary);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-tight);
}

.results-text {
  font-family: var(--font-family-system);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.results-filtered {
  font-family: var(--font-family-system);
  font-weight: var(--font-medium);
  color: var(--text-accent);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.view-options {
  display: flex;
  gap: var(--space-1);
  background: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  padding: var(--space-1);
}

.view-option {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.view-option:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.view-option.active {
  background: var(--plumbob-green);
  color: var(--text-inverse);
  box-shadow: 0 1px 3px rgba(45, 159, 43, 0.3);
}

.view-option svg {
  width: 16px;
  height: 16px;
}

/* Content */
.mod-results {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-6) var(--space-6) var(--space-6);
}

.mod-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--space-6);
}

.mod-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.mod-table-container {
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-light);
  overflow: hidden;
}

/* Loading */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--border-light);
  border-top: 4px solid var(--plumbob-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--space-4);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-family: var(--font-family-system);
  font-size: var(--text-lg);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-16);
  text-align: center;
}

.empty-state__icon {
  width: 64px;
  height: 64px;
  color: var(--text-tertiary);
  margin-bottom: var(--space-4);
}

.empty-state__title {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-3) 0;
}

.empty-state__description {
  font-family: var(--font-family-system);
  font-size: var(--text-base);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0 0 var(--space-6) 0;
  max-width: 480px;
}

.empty-state__action {
  padding: var(--space-3) var(--space-6);
  background: var(--plumbob-green);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
  box-shadow:
    0 2px 4px rgba(0, 212, 170, 0.3),
    var(--shadow-vibrant);
}

.empty-state__action:hover {
  background: var(--plumbob-green-light);
  transform: translateY(-1px);
  box-shadow:
    0 4px 8px rgba(0, 212, 170, 0.4),
    var(--shadow-hover);
}

.empty-state__action:hover {
  background: var(--plumbob-green-dark);
  box-shadow: 0 4px 8px rgba(45, 159, 43, 0.3);
  transform: translateY(-1px);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--space-3);
  }

  .stat-card {
    padding: var(--space-4);
  }

  .stat-card__value {
    font-size: var(--text-3xl);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: var(--space-4);
  }

  .dashboard-controls {
    padding: var(--space-4);
  }

  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }

  .stat-card {
    padding: var(--space-3);
  }

  .stat-card__value {
    font-size: var(--text-2xl);
  }

  .stat-card__label {
    font-size: 10px;
  }

  .search-input-wrapper {
    max-width: none;
  }

  .search-input {
    height: 48px;
    font-size: var(--text-base);
  }

  .filter-section {
    flex-direction: column;
    gap: var(--space-3);
  }

  .filter-group {
    flex-direction: column;
    gap: var(--space-2);
  }

  .mod-grid {
    grid-template-columns: 1fr;
    gap: var(--space-4);
  }

  .results-summary {
    padding: 0 var(--space-4) var(--space-3) var(--space-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }

  .view-options {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: var(--space-3);
  }

  .dashboard-title__main {
    font-size: var(--text-2xl);
  }

  .dashboard-title__subtitle {
    font-size: var(--text-sm);
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--space-2);
  }

  .stat-card__value {
    font-size: var(--text-xl);
  }

  .search-input {
    height: 44px;
    padding: 0 var(--space-10) 0 var(--space-10);
  }

  .search-icon {
    left: var(--space-2);
    width: 18px;
    height: 18px;
  }

  .mod-results {
    padding: 0 var(--space-3) var(--space-3) var(--space-3);
  }
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--space-4);
  margin-top: var(--space-8);
}

.pagination-btn {
  padding: var(--space-2) var(--space-4);
  border: 1px solid var(--border-light);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  cursor: pointer;
  transition: all var(--duration-150) var(--ease-out);
}

.pagination-btn:hover:not(:disabled) {
  background: var(--bg-secondary);
  border-color: var(--border-medium);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-info {
  font-family: var(--font-family-mono);
  font-size: var(--text-sm);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-tight);
}

/* ===== THUMBNAIL GALLERY - APPLE-INSPIRED DESIGN ===== */

.thumbnail-gallery {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 24px 24px 24px; /* 24px padding as per handoff requirements */
}

.thumbnail-grid {
  display: grid;
  gap: 16px; /* 16px gaps as per handoff requirements */
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  align-items: start;
}

/* Responsive thumbnail sizes */
.thumbnail-grid--small {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.thumbnail-grid--medium {
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.thumbnail-grid--large {
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 20px;
}

.thumbnail-item {
  background: var(--bg-primary);
  border-radius: 8px; /* 8px border-radius for modern look as per handoff */
  overflow: hidden;
  transition: all 0.2s ease-out;
  cursor: pointer;
  border: 1px solid var(--border-light);
  position: relative;
  /* Vibrant shadow system for cheerful appearance */
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06),
    var(--shadow-vibrant);
}

/* Elevation hierarchy - Vibrant shadow system */
.thumbnail-item:hover {
  /* Enhanced vibrant shadow on hover for elevated feel */
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06),
    var(--shadow-hover);
  transform: translateY(-2px);
  border-color: var(--border-accent);
}

.thumbnail-item:active {
  /* Pressed state - reduced shadow for tactile feedback */
  box-shadow:
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 1px 1px rgba(0, 0, 0, 0.06);
  transform: translateY(0px);
}

.thumbnail-item:focus {
  outline: none;
  ring: 2px solid var(--plumbob-green);
  ring-offset: 2px;
  /* Maintain elevated shadow on focus */
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.1),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.thumbnail-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.thumbnail-image {
  position: relative;
  width: 100%;
  height: 200px;
  background: var(--bg-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.thumbnail-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease-out;
}

.thumbnail-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
}

.fallback-icon {
  width: 48px;
  height: 48px;
  color: var(--text-tertiary);
}

.thumbnail-overlay {
  padding: 16px; /* Consistent padding */
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  /* Subtle text shadow for better readability */
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.thumbnail-title {
  font-family: var(--font-family-display);
  font-size: var(--text-base);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-snug);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-1) 0;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  hyphens: auto;
}

.thumbnail-author {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Quality and File Type Badges */
.quality-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: var(--font-family-system);
  font-size: var(--text-xs);
  font-weight: var(--font-semibold);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-wide);
  background: var(--plumbob-green);
  color: white;
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  /* Enhanced shadow with vibrant green glow */
  box-shadow:
    0 1px 3px rgba(0, 212, 170, 0.5),
    0 1px 2px rgba(0, 0, 0, 0.1),
    0 0 8px rgba(0, 212, 170, 0.3);
}

.file-type-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: var(--font-family-system);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  line-height: var(--leading-none);
  letter-spacing: var(--tracking-wide);
  background: rgba(255, 255, 255, 0.9);
  color: var(--text-primary);
  backdrop-filter: blur(4px);
  text-transform: uppercase;
  /* Subtle shadow for badge depth */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
}

.file-type-script {
  background: var(--sims-blue);
  color: white;
  /* Enhanced shadow for script badges with Sims blue */
  box-shadow:
    0 1px 3px rgba(9, 132, 227, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

.file-type-package {
  background: var(--sims-purple);
  color: white;
  /* Enhanced shadow for package badges with vibrant purple */
  box-shadow:
    0 1px 3px rgba(162, 155, 254, 0.4),
    0 1px 2px rgba(0, 0, 0, 0.1);
}

/* ===== RESPONSIVE THUMBNAIL DESIGN ===== */

@media (max-width: 1024px) {
  .thumbnail-gallery {
    padding: 0 16px 16px 16px;
  }

  .thumbnail-grid--small {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 10px;
  }

  .thumbnail-grid--medium {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 14px;
  }

  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
  }

  .thumbnail-image {
    height: 180px;
  }

  .thumbnail-overlay {
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .thumbnail-gallery {
    padding: 0 12px 12px 12px;
  }

  .thumbnail-grid--small {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 8px;
  }

  .thumbnail-grid--medium {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 12px;
  }

  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 14px;
  }

  .thumbnail-image {
    height: 160px;
  }

  .thumbnail-overlay {
    padding: 10px;
  }

  .thumbnail-title {
    font-size: var(--text-sm);
    line-height: var(--leading-snug);
    letter-spacing: var(--tracking-normal);
  }

  .thumbnail-author {
    font-size: var(--text-xs);
    line-height: var(--leading-normal);
    letter-spacing: var(--tracking-normal);
  }
}

@media (max-width: 480px) {
  .thumbnail-gallery {
    padding: 0 8px 8px 8px;
  }

  .thumbnail-grid--small,
  .thumbnail-grid--medium,
  .thumbnail-grid--large {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 8px;
  }

  .thumbnail-image {
    height: 140px;
  }

  .thumbnail-overlay {
    padding: 8px;
  }

  .fallback-icon {
    width: 32px;
    height: 32px;
  }

  .quality-badge,
  .file-type-badge {
    padding: 2px 6px;
    font-size: 10px;
    line-height: var(--leading-none);
    letter-spacing: var(--tracking-wide);
  }
}

/* ===== MOD DETAILS MODAL TYPOGRAPHY ===== */

.mod-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: var(--space-4);
  backdrop-filter: blur(4px);
}

.mod-details-content {
  background: var(--bg-elevated);
  border-radius: var(--radius-lg);
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
}

.mod-details-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: var(--space-6);
  border-bottom: 1px solid var(--border-light);
}

.mod-details-title-section {
  flex: 1;
  margin-right: var(--space-4);
}

.mod-details-title {
  font-family: var(--font-family-display);
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-1) 0;
  word-break: break-word;
}

.mod-details-author {
  font-family: var(--font-family-system);
  font-size: var(--text-base);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  margin: 0;
}

.mod-details-close {
  padding: var(--space-2);
  border: none;
  background: none;
  color: var(--text-tertiary);
  cursor: pointer;
  border-radius: var(--radius-sm);
  transition: all var(--duration-150) var(--ease-out);
}

.mod-details-close:hover {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
}

.mod-details-body {
  padding: var(--space-6);
}

.mod-info-grid {
  display: grid;
  gap: var(--space-4);
  margin-bottom: var(--space-6);
}

.mod-info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-3);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
}

.mod-info-label {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
}

.mod-info-value {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-normal);
  letter-spacing: var(--tracking-normal);
  text-align: right;
}

.mod-additional-info h3 {
  font-family: var(--font-family-display);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  line-height: var(--leading-tight);
  letter-spacing: var(--tracking-tight);
  margin: 0 0 var(--space-3) 0;
}

.mod-additional-info p {
  font-family: var(--font-family-system);
  font-size: var(--text-sm);
  font-weight: var(--font-regular);
  color: var(--text-secondary);
  line-height: var(--leading-relaxed);
  letter-spacing: var(--tracking-normal);
  margin: 0 0 var(--space-2) 0;
}

@media (max-width: 768px) {
  .mod-details-modal {
    padding: var(--space-2);
  }

  .mod-details-title {
    font-size: var(--text-xl);
  }

  .mod-details-author {
    font-size: var(--text-sm);
  }

  .mod-details-header,
  .mod-details-body {
    padding: var(--space-4);
  }

  .mod-info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }

  .mod-info-value {
    text-align: left;
  }
}
</style>
